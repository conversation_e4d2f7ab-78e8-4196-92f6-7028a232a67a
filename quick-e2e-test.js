#!/usr/bin/env node

/**
 * Quick End-to-End Testing for Rylie AI Platform
 * 
 * This script tests what's currently available without requiring database setup
 */

import fetch from 'node-fetch';
import { spawn } from 'child_process';

const BASE_URL = 'http://localhost:5000';
const TEST_RESULTS = [];

// Color utilities
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
  console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
}

function logTest(testName, status, details = '') {
  const statusColor = status === 'PASS' ? 'green' : status === 'FAIL' ? 'red' : 'yellow';
  const statusSymbol = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
  
  log(`${statusSymbol} ${testName}: ${status}`, statusColor);
  if (details) {
    log(`   ${details}`, 'blue');
  }
  
  TEST_RESULTS.push({ name: testName, status, details });
}

// Check if server is running
async function isServerRunning() {
  try {
    const response = await fetch(`${BASE_URL}/health`, { timeout: 2000 });
    return response.ok;
  } catch {
    return false;
  }
}

// Test API endpoint
async function testAPI(name, url, options = {}, expectedStatus = 200) {
  const startTime = Date.now();
  
  try {
    const response = await fetch(url, { timeout: 10000, ...options });
    const duration = Date.now() - startTime;
    
    let data = null;
    const contentType = response.headers.get('content-type');
    
    if (contentType && contentType.includes('application/json')) {
      try {
        data = await response.json();
      } catch (e) {
        data = { error: 'Invalid JSON response' };
      }
    } else {
      data = await response.text();
    }
    
    if (response.status === expectedStatus) {
      logTest(name, 'PASS', `Status: ${response.status} (${duration}ms)`);
      return { success: true, data, status: response.status };
    } else {
      logTest(name, 'FAIL', `Expected: ${expectedStatus}, Got: ${response.status}`);
      return { success: false, data, status: response.status };
    }
  } catch (error) {
    const duration = Date.now() - startTime;
    logTest(name, 'FAIL', `Error: ${error.message} (${duration}ms)`);
    return { success: false, error: error.message };
  }
}

// Test basic functionality that should work without database
async function testBasicFunctionality() {
  console.log('\n' + '='.repeat(60));
  log('🧪 TESTING BASIC FUNCTIONALITY', 'bold');
  console.log('='.repeat(60));
  
  // Test static files and frontend
  await testAPI('Frontend Root Page', `${BASE_URL}/`);
  await testAPI('Auth Page', `${BASE_URL}/auth`);
  await testAPI('Login Page', `${BASE_URL}/login`);
  
  // Test API documentation (should work without DB)
  await testAPI('API Documentation', `${BASE_URL}/api/docs`);
  await testAPI('OpenAPI Spec JSON', `${BASE_URL}/api/docs/openapi.json`);
  await testAPI('OpenAPI Spec YAML', `${BASE_URL}/api/docs/openapi.yaml`);
  
  // Test error handling
  await testAPI('404 Error Handling', `${BASE_URL}/nonexistent-page`, {}, 404);
  
  // Test CORS
  await testAPI('CORS Preflight', `${BASE_URL}/health`, { method: 'OPTIONS' });
}

// Test API endpoints that might work without database
async function testAPIEndpoints() {
  console.log('\n' + '='.repeat(60));
  log('🔌 TESTING API ENDPOINTS', 'bold');
  console.log('='.repeat(60));
  
  // Health check should work
  await testAPI('Health Check', `${BASE_URL}/health`);
  
  // Monitoring endpoints
  await testAPI('System Health', `${BASE_URL}/api/monitoring/health`);
  await testAPI('System Metrics', `${BASE_URL}/api/monitoring/metrics`);
  
  // Test endpoints that might return errors but should respond
  await testAPI('Get Conversations', `${BASE_URL}/api/conversations`);
  await testAPI('Get Dealerships', `${BASE_URL}/api/dealerships`);
  await testAPI('Get Users', `${BASE_URL}/api/users`);
  
  // Test POST endpoints
  await testAPI('Create Conversation', `${BASE_URL}/api/conversations`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      customer_name: 'Test Customer',
      customer_phone: '+**********'
    })
  });
  
  // Test inbound message
  await testAPI('Send Inbound Message', `${BASE_URL}/api/inbound`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      message: 'Hello, I am looking for a car',
      customer_name: 'Test Customer',
      customer_phone: '+**********'
    })
  });
}

// Test frontend pages
async function testFrontendPages() {
  console.log('\n' + '='.repeat(60));
  log('🖥️  TESTING FRONTEND PAGES', 'bold');
  console.log('='.repeat(60));
  
  const pages = [
    '/',
    '/auth',
    '/login',
    '/prompt-testing',
    '/enhanced-prompt-testing',
    '/prompt-library',
    '/system',
    '/admin/dealerships',
    '/analytics',
    '/security',
    '/setup'
  ];
  
  for (const page of pages) {
    await testAPI(`Page: ${page}`, `${BASE_URL}${page}`);
  }
}

// Test error scenarios
async function testErrorScenarios() {
  console.log('\n' + '='.repeat(60));
  log('🚫 TESTING ERROR SCENARIOS', 'bold');
  console.log('='.repeat(60));
  
  // Invalid JSON
  await testAPI('Invalid JSON', `${BASE_URL}/api/inbound`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: 'invalid json'
  }, 400);
  
  // Large payload
  const largeMessage = 'x'.repeat(5000);
  await testAPI('Large Payload', `${BASE_URL}/api/inbound`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      message: largeMessage,
      customer_name: 'Test Customer'
    })
  });
  
  // Special characters
  await testAPI('Special Characters', `${BASE_URL}/api/inbound`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      message: 'Test with émojis 🚗 and unicode: àáâãäåæçèéêë',
      customer_name: 'José María'
    })
  });
}

// Generate summary report
function generateSummary() {
  console.log('\n' + '='.repeat(60));
  log('📋 TEST SUMMARY', 'bold');
  console.log('='.repeat(60));
  
  const passed = TEST_RESULTS.filter(t => t.status === 'PASS').length;
  const failed = TEST_RESULTS.filter(t => t.status === 'FAIL').length;
  const warnings = TEST_RESULTS.filter(t => t.status === 'WARN').length;
  const total = TEST_RESULTS.length;
  
  log(`Total Tests: ${total}`, 'cyan');
  log(`✅ Passed: ${passed}`, 'green');
  log(`❌ Failed: ${failed}`, 'red');
  log(`⚠️  Warnings: ${warnings}`, 'yellow');
  log(`📊 Success Rate: ${Math.round((passed / total) * 100)}%`, 'cyan');
  
  if (failed === 0) {
    log('\n🎉 No critical failures detected!', 'green');
  } else {
    log('\n⚠️  Some tests failed - review the issues above', 'yellow');
  }
  
  // Show failed tests
  const failedTests = TEST_RESULTS.filter(t => t.status === 'FAIL');
  if (failedTests.length > 0) {
    log('\n❌ Failed Tests:', 'red');
    failedTests.forEach(test => {
      log(`   • ${test.name}: ${test.details}`, 'red');
    });
  }
  
  return { passed, failed, warnings, total };
}

// Main execution
async function runQuickTests() {
  log('🚀 Starting Quick End-to-End Tests for Rylie AI', 'bold');
  
  // Check if server is running
  if (!(await isServerRunning())) {
    log('❌ Server is not running on port 5000', 'red');
    log('💡 Please start the server with: npm run dev', 'yellow');
    process.exit(1);
  }
  
  log('✅ Server is running, starting tests...', 'green');
  
  try {
    await testBasicFunctionality();
    await testAPIEndpoints();
    await testFrontendPages();
    await testErrorScenarios();
    
    const summary = generateSummary();
    
    // Exit with appropriate code
    process.exit(summary.failed > 0 ? 1 : 0);
    
  } catch (error) {
    log(`❌ Test suite failed: ${error.message}`, 'red');
    console.error(error);
    process.exit(1);
  }
}

// Run the tests
runQuickTests().catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
});
