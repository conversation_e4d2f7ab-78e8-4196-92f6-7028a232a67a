#!/usr/bin/env node

/**
 * Comprehensive End-to-End Testing Suite for Rylie AI Platform
 *
 * This script performs thorough testing of all features, buttons, and functionality
 * to identify errors and ensure the platform is working correctly.
 */

import fetch from 'node-fetch';
import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';

const BASE_URL = 'http://localhost:5000';
const TEST_RESULTS = [];
let SERVER_PROCESS = null;

// Color utilities for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
  dim: '\x1b[2m'
};

function log(message, color = 'reset') {
  const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
  console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
}

function logSection(title) {
  console.log('\n' + '='.repeat(80));
  log(`🧪 ${title}`, 'bold');
  console.log('='.repeat(80));
}

function logTest(testName, status, details = '', duration = null) {
  const statusColor = status === 'PASS' ? 'green' : status === 'FAIL' ? 'red' : 'yellow';
  const statusSymbol = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
  const durationText = duration ? ` (${duration}ms)` : '';

  log(`${statusSymbol} ${testName}: ${status}${durationText}`, statusColor);
  if (details) {
    log(`   ${details}`, 'dim');
  }

  // Store result for final report
  TEST_RESULTS.push({
    name: testName,
    status,
    details,
    duration,
    timestamp: new Date().toISOString()
  });
}

// Utility function to wait for a condition
async function waitFor(condition, timeout = 30000, interval = 1000) {
  const start = Date.now();
  while (Date.now() - start < timeout) {
    if (await condition()) {
      return true;
    }
    await new Promise(resolve => setTimeout(resolve, interval));
  }
  return false;
}

// Check if server is running
async function isServerRunning() {
  try {
    const response = await fetch(`${BASE_URL}/health`, { timeout: 2000 });
    return response.ok;
  } catch {
    return false;
  }
}

// Start the development server
async function startServer() {
  logSection('SERVER STARTUP');

  if (await isServerRunning()) {
    log('Server is already running', 'green');
    return true;
  }

  log('Starting development server...', 'yellow');

  SERVER_PROCESS = spawn('npm', ['run', 'dev'], {
    cwd: process.cwd(),
    stdio: ['pipe', 'pipe', 'pipe'],
    env: { ...process.env, NODE_ENV: 'development' }
  });

  let serverOutput = '';
  let serverError = '';

  SERVER_PROCESS.stdout.on('data', (data) => {
    serverOutput += data.toString();
  });

  SERVER_PROCESS.stderr.on('data', (data) => {
    serverError += data.toString();
  });

  // Wait for server to start
  const serverStarted = await waitFor(isServerRunning, 30000);

  if (serverStarted) {
    log('✅ Server started successfully', 'green');
    return true;
  } else {
    log('❌ Failed to start server', 'red');
    log('Server output:', 'yellow');
    console.log(serverOutput);
    log('Server errors:', 'red');
    console.log(serverError);
    return false;
  }
}

// Stop the server
async function stopServer() {
  if (SERVER_PROCESS) {
    log('Stopping server...', 'yellow');
    SERVER_PROCESS.kill('SIGTERM');

    // Wait for graceful shutdown
    await new Promise(resolve => {
      SERVER_PROCESS.on('exit', resolve);
      setTimeout(() => {
        SERVER_PROCESS.kill('SIGKILL');
        resolve();
      }, 5000);
    });

    SERVER_PROCESS = null;
    log('Server stopped', 'green');
  }
}

// Generic API test function
async function testAPI(name, url, options = {}, expectedStatus = 200) {
  const startTime = Date.now();

  try {
    const response = await fetch(url, {
      timeout: 10000,
      ...options
    });

    const duration = Date.now() - startTime;
    const contentType = response.headers.get('content-type');

    let data = null;
    if (contentType && contentType.includes('application/json')) {
      try {
        data = await response.json();
      } catch (e) {
        data = { error: 'Invalid JSON response' };
      }
    } else {
      data = await response.text();
    }

    if (response.status === expectedStatus) {
      logTest(name, 'PASS', `Status: ${response.status}`, duration);
      return { success: true, data, status: response.status, duration };
    } else {
      logTest(name, 'FAIL', `Expected: ${expectedStatus}, Got: ${response.status}`, duration);
      return { success: false, data, status: response.status, duration };
    }
  } catch (error) {
    const duration = Date.now() - startTime;
    logTest(name, 'FAIL', `Error: ${error.message}`, duration);
    return { success: false, error: error.message, duration };
  }
}

// Test basic API endpoints
async function testBasicAPI() {
  logSection('BASIC API TESTING');

  // Health check
  await testAPI('Health Check', `${BASE_URL}/health`);

  // API documentation
  await testAPI('API Documentation', `${BASE_URL}/api/docs`);

  // OpenAPI spec
  await testAPI('OpenAPI Specification', `${BASE_URL}/api/docs/openapi.json`);

  // 404 handling
  await testAPI('404 Error Handling', `${BASE_URL}/nonexistent-endpoint`, {}, 404);

  // CORS preflight
  await testAPI('CORS Preflight', `${BASE_URL}/health`, { method: 'OPTIONS' });
}

// Test authentication endpoints
async function testAuthentication() {
  logSection('AUTHENTICATION TESTING');

  // Login page
  await testAPI('Login Page', `${BASE_URL}/auth`);

  // Auth status (should be unauthorized)
  await testAPI('Auth Status (Unauthorized)', `${BASE_URL}/api/user`, {}, 401);

  // Magic link endpoint
  await testAPI('Magic Link Request', `${BASE_URL}/api/auth/magic-link`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email: '<EMAIL>' })
  });

  // Login attempt with test credentials
  await testAPI('Login Attempt', `${BASE_URL}/api/auth/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      username: 'testuser',
      password: 'testpass'
    })
  });
}

// Test conversation and messaging features
async function testConversationFeatures() {
  logSection('CONVERSATION & MESSAGING TESTING');

  // Test conversation creation
  await testAPI('Create Conversation', `${BASE_URL}/api/conversations`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      customer_name: 'Test Customer',
      customer_phone: '+1234567890',
      dealership_id: 1
    })
  });

  // Test message sending
  await testAPI('Send Message', `${BASE_URL}/api/inbound`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      message: 'I am looking for a reliable family car under $30,000',
      customer_name: 'Test Customer',
      customer_phone: '+1234567890',
      dealer_id: 1
    })
  });

  // Test conversation retrieval
  await testAPI('Get Conversations', `${BASE_URL}/api/conversations`);

  // Test WebSocket endpoint (if available)
  await testAPI('WebSocket Upgrade', `${BASE_URL}/ws`, {
    headers: {
      'Upgrade': 'websocket',
      'Connection': 'Upgrade',
      'Sec-WebSocket-Key': 'test-key',
      'Sec-WebSocket-Version': '13'
    }
  }, 101);
}

// Test admin and management features
async function testAdminFeatures() {
  logSection('ADMIN & MANAGEMENT TESTING');

  // Dealership management
  await testAPI('Get Dealerships', `${BASE_URL}/api/dealerships`);

  // User management
  await testAPI('Get Users', `${BASE_URL}/api/users`);

  // System monitoring
  await testAPI('System Health', `${BASE_URL}/api/monitoring/health`);

  // Performance metrics
  await testAPI('Performance Metrics', `${BASE_URL}/api/monitoring/metrics`);

  // Database status
  await testAPI('Database Status', `${BASE_URL}/api/monitoring/database`);
}

// Test prompt and AI features
async function testAIFeatures() {
  logSection('AI & PROMPT TESTING');

  // Prompt testing endpoint
  await testAPI('Prompt Testing', `${BASE_URL}/api/prompt-test`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      prompt: 'Test prompt for AI response',
      context: 'automotive sales'
    })
  });

  // Enhanced prompt testing
  await testAPI('Enhanced Prompt Testing', `${BASE_URL}/api/enhanced-prompt-test`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      prompt: 'Enhanced test prompt',
      persona: 'sales_agent',
      context: { dealership: 'Test Motors' }
    })
  });

  // Prompt templates
  await testAPI('Get Prompt Templates', `${BASE_URL}/api/prompt-templates`);
}

// Test inventory and vehicle features
async function testInventoryFeatures() {
  logSection('INVENTORY & VEHICLE TESTING');

  // Vehicle inventory
  await testAPI('Get Vehicle Inventory', `${BASE_URL}/api/vehicles`);

  // Vehicle search
  await testAPI('Search Vehicles', `${BASE_URL}/api/vehicles/search`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      make: 'Toyota',
      model: 'Camry',
      max_price: 30000
    })
  });

  // Inventory import
  await testAPI('Inventory Import Status', `${BASE_URL}/api/inventory/import-status`);
}

// Test analytics and reporting
async function testAnalyticsFeatures() {
  logSection('ANALYTICS & REPORTING TESTING');

  // Analytics dashboard
  await testAPI('Analytics Dashboard', `${BASE_URL}/api/analytics/dashboard`);

  // Conversation analytics
  await testAPI('Conversation Analytics', `${BASE_URL}/api/analytics/conversations`);

  // Lead analytics
  await testAPI('Lead Analytics', `${BASE_URL}/api/analytics/leads`);

  // Performance reports
  await testAPI('Performance Reports', `${BASE_URL}/api/reports/performance`);
}

// Test frontend pages and components
async function testFrontendPages() {
  logSection('FRONTEND PAGES TESTING');

  const pages = [
    '/',
    '/auth',
    '/login',
    '/prompt-testing',
    '/enhanced-prompt-testing',
    '/prompt-library',
    '/system',
    '/admin/dealerships',
    '/admin/branding',
    '/analytics',
    '/security',
    '/setup'
  ];

  for (const page of pages) {
    await testAPI(`Frontend Page: ${page}`, `${BASE_URL}${page}`);
  }
}

// Test error scenarios and edge cases
async function testErrorScenarios() {
  logSection('ERROR SCENARIOS & EDGE CASES');

  // Invalid JSON
  await testAPI('Invalid JSON Request', `${BASE_URL}/api/inbound`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: 'invalid json'
  }, 400);

  // Missing required fields
  await testAPI('Missing Required Fields', `${BASE_URL}/api/conversations`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({})
  }, 400);

  // Large payload
  const largePayload = 'x'.repeat(10000);
  await testAPI('Large Payload', `${BASE_URL}/api/inbound`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      message: largePayload,
      customer_name: 'Test Customer'
    })
  });

  // Special characters
  await testAPI('Special Characters', `${BASE_URL}/api/inbound`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      message: 'Test with special chars: àáâãäåæçèéêë 中文 🚗',
      customer_name: 'José María'
    })
  });
}

// Performance testing
async function testPerformance() {
  logSection('PERFORMANCE TESTING');

  const performanceTests = [
    { name: 'Health Check Performance', url: `${BASE_URL}/health` },
    { name: 'API Response Performance', url: `${BASE_URL}/api/conversations` },
    { name: 'Frontend Load Performance', url: `${BASE_URL}/` }
  ];

  for (const test of performanceTests) {
    const results = [];

    // Run test 5 times
    for (let i = 0; i < 5; i++) {
      const result = await testAPI(`${test.name} (Run ${i + 1})`, test.url);
      if (result.duration) {
        results.push(result.duration);
      }
    }

    if (results.length > 0) {
      const avgDuration = results.reduce((a, b) => a + b, 0) / results.length;
      const maxDuration = Math.max(...results);
      const minDuration = Math.min(...results);

      log(`📊 ${test.name} Summary:`, 'cyan');
      log(`   Average: ${avgDuration.toFixed(2)}ms`, 'dim');
      log(`   Min: ${minDuration}ms, Max: ${maxDuration}ms`, 'dim');

      if (avgDuration < 1000) {
        logTest(`${test.name} Average`, 'PASS', `${avgDuration.toFixed(2)}ms`);
      } else {
        logTest(`${test.name} Average`, 'WARN', `${avgDuration.toFixed(2)}ms (slow)`);
      }
    }
  }
}

// Generate comprehensive test report
async function generateTestReport() {
  logSection('TEST REPORT GENERATION');

  const passedTests = TEST_RESULTS.filter(t => t.status === 'PASS').length;
  const failedTests = TEST_RESULTS.filter(t => t.status === 'FAIL').length;
  const warnTests = TEST_RESULTS.filter(t => t.status === 'WARN').length;
  const totalTests = TEST_RESULTS.length;

  const report = {
    summary: {
      total: totalTests,
      passed: passedTests,
      failed: failedTests,
      warnings: warnTests,
      successRate: Math.round((passedTests / totalTests) * 100),
      timestamp: new Date().toISOString()
    },
    results: TEST_RESULTS,
    recommendations: []
  };

  // Add recommendations based on results
  if (failedTests > 0) {
    report.recommendations.push('Address failed tests to ensure platform stability');
  }
  if (warnTests > 0) {
    report.recommendations.push('Review warnings for potential improvements');
  }

  // Save report to file
  const reportPath = path.join(process.cwd(), 'e2e-test-report.json');
  await fs.writeFile(reportPath, JSON.stringify(report, null, 2));

  log(`📄 Test report saved to: ${reportPath}`, 'green');

  // Display summary
  console.log('\n' + '='.repeat(80));
  log('📋 FINAL TEST SUMMARY', 'bold');
  console.log('='.repeat(80));

  log(`Total Tests: ${totalTests}`, 'white');
  log(`✅ Passed: ${passedTests}`, 'green');
  log(`❌ Failed: ${failedTests}`, 'red');
  log(`⚠️  Warnings: ${warnTests}`, 'yellow');
  log(`📊 Success Rate: ${report.summary.successRate}%`, 'cyan');

  if (passedTests === totalTests) {
    log('\n🎉 All tests passed! Platform is working correctly.', 'green');
  } else if (failedTests === 0) {
    log('\n✅ No critical failures, but some warnings to review.', 'yellow');
  } else {
    log('\n⚠️  Some tests failed. Please review the issues above.', 'red');
  }

  return report;
}

// Main test execution function
async function runComprehensiveTests() {
  log('🚀 Starting Comprehensive End-to-End Testing Suite', 'bold');
  log('Platform: Rylie AI - Automotive Dealership Conversational AI', 'cyan');

  try {
    // Start server if needed
    const serverRunning = await startServer();
    if (!serverRunning) {
      log('❌ Cannot proceed without server. Exiting.', 'red');
      process.exit(1);
    }

    // Run all test suites
    await testBasicAPI();
    await testAuthentication();
    await testConversationFeatures();
    await testAdminFeatures();
    await testAIFeatures();
    await testInventoryFeatures();
    await testAnalyticsFeatures();
    await testFrontendPages();
    await testErrorScenarios();
    await testPerformance();

    // Generate final report
    const report = await generateTestReport();

    // Cleanup
    await stopServer();

    // Exit with appropriate code
    process.exit(report.summary.failed > 0 ? 1 : 0);

  } catch (error) {
    log(`❌ Test suite failed with error: ${error.message}`, 'red');
    console.error(error);

    await stopServer();
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  log('\n🛑 Test suite interrupted', 'yellow');
  await stopServer();
  process.exit(1);
});

process.on('SIGTERM', async () => {
  log('\n🛑 Test suite terminated', 'yellow');
  await stopServer();
  process.exit(1);
});

// Run the comprehensive test suite
runComprehensiveTests().catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
});
