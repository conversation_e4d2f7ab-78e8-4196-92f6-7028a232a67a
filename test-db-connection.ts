// Load environment variables first
import dotenv from 'dotenv';
dotenv.config();

import { checkDatabaseConnection, executeQuery } from './server/db';

async function testDatabaseConnection() {
  try {
    console.log('Testing database connection...');
    const isConnected = await checkDatabaseConnection();
    console.log('Database connection test result:', isConnected ? 'CONNECTED' : 'FAILED');
    
    if (isConnected) {
      console.log('Testing executeQuery function...');
      const result = await executeQuery(async () => {
        return { success: true, message: 'Query executed successfully' };
      });
      console.log('Query result:', result);
    }
    
    return isConnected;
  } catch (error) {
    console.error('Error testing database connection:', error);
    return false;
  }
}

testDatabaseConnection()
  .then(result => {
    console.log('Test completed with result:', result);
    process.exit(result ? 0 : 1);
  })
  .catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });